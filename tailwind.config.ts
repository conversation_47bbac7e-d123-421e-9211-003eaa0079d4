import type {Config} from 'tailwindcss';

const config: Config = {
  // Removed darkMode since you're forcing dark mode in layout
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    '*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    // Set Arial as the default font family for all text
    fontFamily: {
      sans: ['Arial', 'Helvetica', 'sans-serif'],
    },
    extend: {
      // Only the fonts you actually use
      fontFamily: {
        'league-gothic': ['var(--font-league-gothic)', 'League Gothic', 'Arial Black', 'sans-serif'],
        'alex-brush': ['var(--font-alex-brush)', 'Alex Brush', 'cursive'],
        'arial': ['Arial', 'Helvetica', 'sans-serif'],
      },
      // Only the colors you actually use
      colors: {
        'penn-blue': '#101048',
        'syracuse-orange': '#D14600',
        'steel-pink': '#DE1ACE',
        'lavender-pink': '#FCADE7',
        'papaya-whip': '#FDF0DA',
        // Keep minimal shadcn colors only if you're using shadcn components
        'border': 'hsl(var(--border))',
        'input': 'hsl(var(--input))',
        'ring': 'hsl(var(--ring))',
        'background': 'hsl(var(--background))',
        'foreground': 'hsl(var(--foreground))',
        'primary': {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        'accent': {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
      },
      // Simplified border radius - only if you're using CSS variables
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  // Only include plugins you actually use
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;
